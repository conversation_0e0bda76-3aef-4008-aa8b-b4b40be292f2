﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using uBuyFirst.Data;
using uBuyFirst.Prefs;
using uBuyFirst.Restocker.Data;
using uBuyFirst.Restocker.Services;

namespace uBuyFirst.Filters
{
    /// <summary>
    /// Filter action that triggers automatic purchase attempts for items matching criteria
    /// </summary>
    public class RestockFilterAction : IFilterAction
    {
        public const string IDENTIFIER = "RESTOCK";

        private PurchaseExecutionService _purchaseService;
        private IPurchaseTrackerRepository _repository;

        public string DisplayName => "Restock";
        public string ActionTypeIdentifier => IDENTIFIER;

        /// <summary>
        /// Job ID for tracking purchases (optional)
        /// </summary>
        public string JobId { get; set; }

        /// <summary>
        /// Whether to enable purchase execution (for testing/debugging)
        /// </summary>
        public bool EnablePurchasing { get; set; } = true;

        public async Task<FilterActionResult> ExecuteAsync(IFilterActionContext context)
        {
            if (context?.FilterRule == null || context.SourceDataTable == null)
            {
                return FilterActionResult.CreateFailure("Invalid context provided to Restock filter");
            }

            try
            {
                var processedCount = 0;
                var purchaseAttempts = 0;
                var successfulPurchases = 0;

                // Collect all matching items first
                var matchingItems = new List<(DataList dataList, string filterAlias)>();

                for (int i = 0; i < context.SourceDataTable.Rows.Count; i++)
                {
                    var row = context.SourceDataTable.Rows[i];

                    // Check if this row matches the filter criteria
                    if (context.FilterRule.GetEvaluator().Fit(row))
                    {
                        processedCount++;

                        // Extract DataList from the row
                        if (row["Blob"] is DataList dataList)
                        {
                            matchingItems.Add((dataList, context.FilterRule.Alias));
                        }
                    }
                }

                // Process purchase attempts sequentially to avoid overwhelming the system
                // (You could make this concurrent by using Task.WhenAll if desired)
                foreach (var (dataList, filterAlias) in matchingItems)
                {
                    var result = await TryPurchaseItemAsync(dataList, filterAlias);
                    purchaseAttempts++;
                    if (result.Success)
                    {
                        successfulPurchases++;
                    }
                }

                var message = $"Restock filter '{context.FilterRule.Alias}' processed {processedCount} matching items. " +
                             $"Purchase attempts: {purchaseAttempts}, Successful: {successfulPurchases}";

                return FilterActionResult.CreateSuccess(message, processedCount);
            }
            catch (Exception ex)
            {
                return FilterActionResult.CreateFailure($"Restock filter '{context.FilterRule.Alias}' failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Attempts to purchase an item using the purchase execution service
        /// </summary>
        private async Task<PurchaseExecutionResult> TryPurchaseItemAsync(DataList dataList, string filterAlias)
        {
            if (!EnablePurchasing)
            {
                return PurchaseExecutionResult.CreateSkipped("Purchase execution is disabled");
            }

            try
            {
                // Initialize services if needed
                EnsureServicesInitialized();

                // Find the keyword that generated this DataList
                var keyword = FindKeywordByAlias(dataList.Term);

                if (keyword == null)
                {
                    return PurchaseExecutionResult.CreateSkipped($"Could not find keyword for alias: {dataList.Term}");
                }

                // Check if keyword has restock configuration
                if (string.IsNullOrEmpty(keyword.JobId) || keyword.RequiredQuantity <= 0)
                {
                    return PurchaseExecutionResult.CreateSkipped("Keyword has no restock configuration");
                }

                // Execute the purchase attempt
                return await _purchaseService.TryPurchaseItemAsync(dataList, keyword, filterAlias);
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("RestockEnabled is false"))
            {
                return PurchaseExecutionResult.CreateSkipped("Restock functionality is disabled. Enable RestockerEnabled in ConnectionConfig to use this feature.");
            }
            catch (Exception ex)
            {
                return PurchaseExecutionResult.CreateFailure($"Error in purchase attempt: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Finds a keyword by its alias
        /// </summary>
        private Keyword2Find FindKeywordByAlias(string alias)
        {
            if (string.IsNullOrEmpty(alias))
                return null;

            // Access the keywords collection from Form1
            var form1 = Form1.Instance;
            if (form1?.EbaySearches?.ChildrenCore == null)
                return null;

            return form1.EbaySearches.ChildrenCore.FirstOrDefault(kw => kw.Alias == alias);
        }

        /// <summary>
        /// Ensures that the required services are initialized
        /// </summary>
        private void EnsureServicesInitialized()
        {
            if (_repository == null)
            {
                _repository = new PurchaseTrackerRepository();
            }

            if (_purchaseService == null)
            {
                _purchaseService = new PurchaseExecutionService(_repository);
            }
        }

        public bool ValidateConfiguration(XFilterClass filter, out string? errorMessage)
        {
            errorMessage = null;

            // Validate JobId if specified
            if (!string.IsNullOrEmpty(JobId) && JobId.Length > 100)
            {
                errorMessage = "Job ID must be 100 characters or less";
                return false;
            }

            return true;
        }

        public Dictionary<string, object> SerializeActionData()
        {
            var data = new Dictionary<string, object>();

            if (!string.IsNullOrEmpty(JobId))
            {
                data["JobId"] = JobId;
            }

            data["EnablePurchasing"] = EnablePurchasing;

            return data;
        }

        public void DeserializeActionData(Dictionary<string, object> data)
        {
            if (data == null) return;

            if (data.TryGetValue("JobId", out var jobIdObj) && jobIdObj != null)
            {
                JobId = jobIdObj.ToString();
            }

            if (data.TryGetValue("EnablePurchasing", out var enablePurchasingObj) && enablePurchasingObj != null)
            {
                if (bool.TryParse(enablePurchasingObj.ToString(), out var enablePurchasing))
                {
                    EnablePurchasing = enablePurchasing;
                }
            }
        }

        /// <summary>
        /// Loads action-specific data from the filter
        /// </summary>
        public void LoadFromFilter(XFilterClass filter)
        {
            if (filter?.ActionData != null)
            {
                DeserializeActionData(filter.ActionData);
            }
        }

        /// <summary>
        /// Saves action-specific data to the filter
        /// </summary>
        public void SaveToFilter(XFilterClass filter)
        {
            if (filter != null)
            {
                filter.ActionData = SerializeActionData();
            }
        }

        /// <summary>
        /// Disposes of resources when the action is no longer needed
        /// </summary>
        public void Dispose()
        {
            _purchaseService?.Dispose();
            _repository?.Dispose();
        }
    }

    /// <summary>
    /// UI configurator for the Restock filter action
    /// </summary>
    public class RestockFilterUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => RestockFilterAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = false,
                ShowFormatControls = false,
                AdditionalControlsToShow = new List<string>
                {
                    // Add any specific UI controls needed for Restock configuration
                    // For now, we'll use the basic configuration
                }
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // Load any UI-specific data from the filter
            // The action-specific data is handled by the action itself
            if (filter?.ActionHandler is RestockFilterAction restockAction)
            {
                // Load any UI-specific settings if needed
                // For now, the action handles its own data loading
            }
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // Save any UI-specific data to the filter
            // The action-specific data is handled by the action itself
            if (filter?.ActionHandler is RestockFilterAction restockAction)
            {
                // Save any UI-specific settings if needed
                // For now, the action handles its own data saving
                restockAction.SaveToFilter(filter);
            }
        }
    }
}
