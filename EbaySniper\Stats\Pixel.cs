﻿using System;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using uBuyFirst.Prefs;
using uBuyFirst.Tools;

namespace uBuyFirst.Stats
{
    static class Pixel
    {
        internal enum EventType
        {
            <PERSON><PERSON>er,
            PlaceOfferWin,
            PlaceOfferLost,
            MakeOffer,
            PaypalWin,
            PaypalLost,
            MakeOfferWeb,
        }

        public static void Track(EventType eventType, string itemID, double amount = 0)
        {
            try
            {
                var url = Config.PixelUrl;
                var hwid = ProgramState.HWID ?? "";
                var serialNumber = ProgramState.SerialNumber ?? "";
                if (string.IsNullOrEmpty(hwid) && string.IsNullOrEmpty(serialNumber))
                {
                    hwid = "HWID";
                    serialNumber = "SN";
                }

                var data = new
                {
                    TimeStamp = (int)Helpers.ConvertToUnixTimestamp(DateTime.UtcNow),
                    EventType = eventType.ToString(),
                    HWID = hwid,
                    License = serialNumber,
                    ItemID = itemID,
                    Amount = amount
                };
                var base64String = Convert.ToBase64String(Encoding.ASCII.GetBytes(JsonConvert.SerializeObject(data)));
                base64String = new string(base64String.Reverse().ToArray());
                var postData = "data=" + base64String;
                _ = FetchUrlAsync(url, postData);
            }
            catch (Exception)
            {
                // ignored
            }
        }

        public static async Task FetchUrlAsync(string url, string postData)
        {
            try
            {
                byte[] buffer = Encoding.ASCII.GetBytes(postData);
                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
                request.Method = "POST";
                request.ContentType = "application/x-www-form-urlencoded";
                request.ContentLength = buffer.Length;

                using (Stream stream = await request.GetRequestStreamAsync())
                {
                    await stream.WriteAsync(buffer, 0, buffer.Length);
                }

                // Get and return response
                using (var response = await request.GetResponseAsync())
                {
                    using (var responseStream = response.GetResponseStream())
                    {
                        if (responseStream != null)
                        {
                            using (var reader = new StreamReader(responseStream))
                            {
                                var result = await reader.ReadToEndAsync();
                                // Handle the result as needed
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle the exception as needed
            }
        }
    }
}
